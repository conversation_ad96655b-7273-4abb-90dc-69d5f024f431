import asyncio
from src.database.connection import get_database

async def check_execution():
    db = await get_database()
    collection = db['executions']
    
    execution_id = 'e09f1d21-ac16-449a-ae83-8047305f968c'
    exec_doc = await collection.find_one({'execution_id': execution_id})
    
    print(f'Execution found: {exec_doc is not None}')
    if exec_doc:
        print(f'AI insights: {exec_doc.get("ai_insights")}')
        print(f'Test analysis: {exec_doc.get("test_analysis")}')
        print(f'Status: {exec_doc.get("status")}')
        print(f'Metadata keys: {list(exec_doc.get("metadata", {}).keys())}')
        
        # Check if ai_insights is in metadata
        metadata = exec_doc.get('metadata', {})
        if 'ai_insights' in metadata:
            print(f'AI insights in metadata: {metadata["ai_insights"]}')
        else:
            print('AI insights not found in metadata')
    else:
        print(f'Execution {execution_id} not found in MongoDB')

if __name__ == '__main__':
    asyncio.run(check_execution())