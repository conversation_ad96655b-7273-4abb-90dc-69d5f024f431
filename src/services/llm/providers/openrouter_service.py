"""
OpenRouter Service Provider - Optimizado para minimizar costos
Implementa la interfaz BaseLLMService con enfoque en modelos gratuitos MÁS POPULARES
"""

import os
import logging
import base64
from typing import Dict, Any, List, Optional
from enum import Enum
from ..base_llm_service import BaseLLMService, LLMRequest, LLMResponse

logger = logging.getLogger(__name__)

# Try to import OpenRouter client - fallback to requests if not available
try:
    from openrouter_client import OpenRouterClient
    from openrouter_client.exceptions import AuthenticationError, RateLimitError, ValidationError
    OPENROUTER_AVAILABLE = True
    USE_REQUESTS_FALLBACK = False
except ImportError:
    try:
        # Alternative: try different import structure
        from openrouter import OpenRouter as OpenRouterClient
        OPENROUTER_AVAILABLE = True
        USE_REQUESTS_FALLBACK = False
    except ImportError:
        # Use requests implementation (our primary implementation)
        import requests
        OPENROUTER_AVAILABLE = True
        USE_REQUESTS_FALLBACK = True


class ModelTier(Enum):
    """Model tier classification for cost control."""
    FREE = "free"
    CHEAP = "cheap"
    PREMIUM = "premium"


class OpenRouterService:
    """OpenRouter LLM service provider with aggressive cost optimization using TOP FREE models."""
    
    def __init__(self, api_key: Optional[str] = None, 
                 max_monthly_spend: float = 0.0,
                 prefer_free_models: bool = True,
                 fast_fail_mode: bool = True):
        """Initialize OpenRouter service with cost controls.
        
        Args:
            api_key: OpenRouter API key (defaults to env OPENROUTER_API_KEY)
            max_monthly_spend: Maximum monthly spend limit (0 = free only)
            prefer_free_models: Always try free models first
            fast_fail_mode: Limit model attempts to prevent API blocking (recommended for web APIs)
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.max_monthly_spend = max_monthly_spend
        self.prefer_free_models = prefer_free_models
        self.fast_fail_mode = fast_fail_mode
        self._client = None
        self._available = False
        
        if not OPENROUTER_AVAILABLE:
            logger.warning("OpenRouter client library not available")
            return
        
        if self.api_key:
            try:
                if USE_REQUESTS_FALLBACK:
                    self._client = self._create_requests_client()
                    self._available = True
                else:
                    # Use official client with cost controls
                    self._client = OpenRouterClient(
                        api_key=self.api_key,
                        max_retries=3,  # Reduced retries to avoid costs
                        rate_limit_buffer=0.5,  # More conservative buffer
                        timeout=30.0  # Shorter timeout
                    )
                    self._available = True
                    logger.info("OpenRouter service initialized with TOP FREE models")
            except Exception as e:
                logger.error(f"Failed to initialize OpenRouter service: {e}")
                self._available = False
        
        # Model configurations prioritizing TOP FREE models by weekly usage
        self.model_configs = {
            "gherkin": {
                "models": [
                    # TOP FREE models by weekly usage - $0.00 cost
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # #1 for coding
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # #1 reasoning
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # 70B powerhouse
                    ("deepseek/deepseek-r1-distill-llama-70b:free", ModelTier.FREE),  # Popular distill
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google experimental
                    ("deepseek/deepseek-r1-zero:free", ModelTier.FREE),  # Zero-shot specialist
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Math reasoning
                    # Cheap fallbacks only if free models fail
                    ("openai/gpt-3.5-turbo", ModelTier.CHEAP),
                    ("anthropic/claude-3-haiku", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 1024,  # Reduced to minimize costs
                "cost_priority": "minimize"
            },
            "validation": {
                "models": [
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Best reasoning for validation
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Reliable performer
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Strong baseline
                    ("deepseek/deepseek-r1-zero:free", ModelTier.FREE),  # Zero-shot specialist
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google reasoning
                    ("openai/gpt-3.5-turbo", ModelTier.CHEAP),
                ],
                "temperature": 0.0,
                "max_tokens": 512,  # Very conservative for validation
                "cost_priority": "minimize"
            },
            "translation": {
                "models": [
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google excels at translation
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Multilingual capable
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Good multilingual
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Strong in multiple languages
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Advanced reasoning
                    ("openai/gpt-3.5-turbo", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 512,
                "cost_priority": "minimize"
            },
            "enhancement": {
                "models": [
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Best for complex reasoning
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Great for enhancement
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Strong general model
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Creative enhancement
                    ("deepseek/deepseek-r1-distill-llama-70b:free", ModelTier.FREE),  # Distilled power
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Mathematical reasoning
                    ("openai/gpt-4o-mini", ModelTier.CHEAP),
                    ("anthropic/claude-3-haiku", ModelTier.CHEAP),
                ],
                "temperature": 0.2,
                "max_tokens": 1024,
                "cost_priority": "balance"
            },
            "test_analysis": {
                "models": [
                    # TOP FREE vision models - restore Llama 4 and others
                    ("meta-llama/llama-4-maverick:free", ModelTier.FREE),  # 400B MoE - user requested
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Most reliable
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Very stable
                    ("mistralai/mistral-small-3.1-24b-instruct:free", ModelTier.FREE),  # Vision capable
                    ("moonshotai/kimi-vl-a3b-thinking:free", ModelTier.FREE),  # Visual reasoning
                    ("qwen/qwen2.5-vl-3b-instruct:free", ModelTier.FREE),  # Compact vision
                    # Quick fallback to paid if budget allows
                    ("openai/gpt-4o-mini", ModelTier.CHEAP),
                    ("google/gemini-1.5-flash", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 1024,  # Conservative limit
                "cost_priority": "minimize"
            },
            "script_generation": {
                "models": [
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # #1 for coding
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Advanced reasoning for complex scripts
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Strong coding model
                    ("deepseek/deepseek-r1-distill-llama-70b:free", ModelTier.FREE),  # Distilled coding power
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google coding
                    ("openai/gpt-4o-mini", ModelTier.CHEAP),
                    ("anthropic/claude-3-haiku", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 2048,
                "cost_priority": "balance"
            },
            "general": {
                "models": [
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Most popular general model
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Best reasoning
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Strong general purpose
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google's latest
                    ("deepseek/deepseek-r1-zero:free", ModelTier.FREE),  # Zero-shot capability
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Mathematical reasoning
                    ("openai/gpt-4o-mini", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 1024,
                "cost_priority": "minimize"
            }
        }
    
    def _create_requests_client(self):
        """Create a requests-based client with cost tracking headers."""
        return {
            "api_key": self.api_key,
            "base_url": "https://openrouter.ai/api/v1",
            "headers": {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/nahuelcioffi/qak",
                "X-Title": "QAK Agent - TOP FREE Models Optimized",
            }
        }
    
    def _check_spending_limit(self) -> bool:
        """Check if we're within spending limits."""
        if self.max_monthly_spend == 0.0:
            logger.info("FREE-ONLY mode: Using only TOP $0.00 models")
            return True  # Free-only mode
        
        try:
            if not USE_REQUESTS_FALLBACK and hasattr(self._client, 'credits'):
                credits = self._client.credits.get()
                monthly_usage = getattr(credits.data, 'usage', 0)
                
                if monthly_usage >= self.max_monthly_spend:
                    logger.warning(f"Monthly spend limit reached: ${monthly_usage:.4f}")
                    return False
        except Exception as e:
            logger.warning(f"Could not check spending limit: {e}")
        
        return True
    
    def _filter_models_by_budget(self, models: List[tuple], has_images: bool = False) -> List[str]:
        """Filter models based on budget constraints."""
        if not self._check_spending_limit():
            return []  # No models if over budget
        
        filtered_models = []
        
        # If max spend is 0, only use free models
        if self.max_monthly_spend == 0.0:
            for model, tier in models:
                if tier == ModelTier.FREE:
                    filtered_models.append(model)
            logger.info(f"FREE-ONLY: Selected {len(filtered_models)} TOP free models")
        else:
            # Use all available models within budget, but prioritize free
            if self.prefer_free_models:
                # Add free models first
                for model, tier in models:
                    if tier == ModelTier.FREE:
                        filtered_models.append(model)
                # Then add paid models
                for model, tier in models:
                    if tier != ModelTier.FREE and model not in filtered_models:
                        filtered_models.append(model)
            else:
                # Use models as ordered
                filtered_models = [model for model, tier in models]
        
        # For vision tasks, filter to vision-capable models
        if has_images:
            vision_capable = []
            # Restored TOP FREE vision models as requested
            stable_free_vision = [
                "meta-llama/llama-4-maverick:free",  # User specifically requested Llama 4
                "deepseek/deepseek-chat-v3-0324:free",  # Can handle simple vision 
                "meta-llama/llama-3.3-70b-instruct:free",  # Can handle some vision
                "mistralai/mistral-small-3.1-24b-instruct:free",  # Vision capable
                "moonshotai/kimi-vl-a3b-thinking:free",  # Visual reasoning specialist
                "qwen/qwen2.5-vl-3b-instruct:free"  # Compact vision model
            ]
            
            for model in filtered_models:
                # Check if it's a known vision model (free or paid)
                if (model in stable_free_vision or 
                    (":free" not in model and any(x in model.lower() for x in ["gpt-4", "gemini", "claude", "vision"]))):
                    vision_capable.append(model)
            
            if not vision_capable and self.max_monthly_spend > 0.0:
                logger.warning("No free vision models available, adding cheapest paid options")
                # Add cheapest vision models if no free options and budget allows
                cheap_vision = ["openai/gpt-4o-mini", "google/gemini-1.5-flash", "anthropic/claude-3-haiku"]
                for model in cheap_vision:
                    if model not in vision_capable:
                        vision_capable.append(model)
            elif not vision_capable:
                logger.warning("No free vision models available in free-only mode - will try text models")
                # In free-only mode, fallback to best text models for image analysis
                return filtered_models[:3]  # Return top 3 text models to attempt vision
            
            return vision_capable
        
        return filtered_models
    
    def _make_requests_based_call(self, request_params: Dict[str, Any]) -> Dict[str, Any]:
        """Make OpenRouter API call using aiohttp for async support."""
        import asyncio
        import aiohttp
        
        model = request_params.get("model", "unknown")
        is_free = ":free" in model
        
        # Enhanced logging with popularity info
        top_models = [
            "meta-llama/llama-4-maverick:free",  # User requested Llama 4
            "deepseek/deepseek-r1:free",
            "deepseek/deepseek-chat-v3-0324:free",
            "meta-llama/llama-3.3-70b-instruct:free"
        ]
        popularity = "🔥 TOP" if model in top_models else "📊 POPULAR" if is_free else "💰 PAID"
        
        logger.info(f"Making request to {popularity} {'FREE $0.00' if is_free else 'PAID'} model: {model} [8s timeout]")
        
        url = f"{self._client['base_url']}/chat/completions"
        
        # Run async request in event loop
        async def make_async_request():
            timeout = aiohttp.ClientTimeout(total=8)  # Very aggressive timeout to prevent blocking
            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    async with session.post(
                        url,
                        headers=self._client["headers"],
                        json=request_params
                    ) as response:
                        if response.status == 401:
                            raise Exception("Authentication error - check OPENROUTER_API_KEY")
                        elif response.status == 429:
                            raise Exception("Rate limit exceeded")
                        elif response.status != 200:
                            error_text = await response.text()
                            raise Exception(f"HTTP {response.status}: {error_text}")
                        
                        result = await response.json()
                        return result
                except asyncio.TimeoutError:
                    raise Exception(f"Request timeout (8s) - Model overloaded, trying next")
                except Exception as e:
                    raise e
        
        # Get or create event loop
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # Run the async request
        if loop.is_running():
            # If we're already in an async context, create a new thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, make_async_request())
                result = future.result()
        else:
            result = loop.run_until_complete(make_async_request())
        
        # Log usage for cost tracking
        if "usage" in result:
            usage = result["usage"]
            cost_info = "FREE $0.00" if is_free else "PAID"
            logger.info(f"✅ {model} - Tokens: {usage.get('total_tokens', 0)} ({cost_info})")
        
        return result
    
    def make_request(self, request: LLMRequest) -> LLMResponse:
        """Make request to OpenRouter with aggressive cost optimization using TOP models."""
        if not self.is_available():
            return LLMResponse(
                content="",
                model_used="openrouter",
                success=False,
                error="OpenRouter service not available"
            )
        
        use_case = request.use_case
        messages = request.messages
        has_images = request.has_images
        
        # Get configuration for use case
        config = self.model_configs.get(use_case, self.model_configs["general"])
        
        # Filter models by budget
        available_models = self._filter_models_by_budget(config["models"], has_images)
        
        if not available_models:
            return LLMResponse(
                content="",
                model_used="none",
                success=False,
                error="No TOP free models available within budget constraints"
            )
        
        # Try each model in sequence with anti-blocking controls
        last_error = None
        max_models_to_try = 3 if self.fast_fail_mode else len(available_models)  # Allow 3 tries in fast-fail
        models_tried = 0
        
        if self.fast_fail_mode:
            logger.info(f"🚀 FAST-FAIL mode: Will try up to {max_models_to_try} models with quick timeouts")
        
        for model in available_models:
            if models_tried >= max_models_to_try:
                logger.warning(f"⏱️ Stopping after {max_models_to_try} attempts (fast-fail mode: {self.fast_fail_mode})")
                break
                
            try:
                models_tried += 1
                is_free = ":free" in model
                
                # Enhanced logging with popularity status
                top_models = [
                    "meta-llama/llama-4-maverick:free",  # User requested Llama 4
                    "deepseek/deepseek-r1:free",
                    "deepseek/deepseek-chat-v3-0324:free",
                    "meta-llama/llama-3.3-70b-instruct:free"
                ]
                popularity = "🔥 TOP" if model in top_models else "📊 POPULAR" if is_free else "💰 PAID"
                
                logger.info(f"Trying {popularity} {'FREE $0.00' if is_free else 'PAID'} model {model} for {use_case} ({models_tried}/{max_models_to_try}) [8s timeout]")
                
                # --- SERIALIZACION DE MENSAJES PARA OPENROUTER ---
                # Convertir MessageContent a dict si es necesario
                serializable_messages = []
                for msg in messages:
                    msg_copy = msg.copy()
                    if isinstance(msg_copy.get("content"), list):
                        msg_copy["content"] = [message_content_to_dict(c) for c in msg_copy["content"]]
                    serializable_messages.append(msg_copy)
                # ------------------------------------------------

                # Prepare request with conservative limits
                request_params = {
                    "model": model,
                    "messages": serializable_messages,
                    "temperature": request.temperature if request.temperature is not None else config.get("temperature", 0.1),
                    "max_tokens": min(request.max_tokens, config.get("max_tokens", 1024)) if request.max_tokens is not None else config.get("max_tokens", 1024)
                }
                
                # Make request
                if USE_REQUESTS_FALLBACK:
                    response_data = self._make_requests_based_call(request_params)
                    content = response_data["choices"][0]["message"]["content"]
                    usage = response_data.get("usage")
                else:
                    response = self._client.chat.create(**request_params)
                    content = response.choices[0].message.content
                    usage = response.usage.dict() if response.usage else None
                
                logger.info(f"✅ SUCCESS with {popularity} {'FREE $0.00' if is_free else 'PAID'} model {model}")
                
                return LLMResponse(
                    content=content,
                    model_used=model,
                    usage=usage,
                    metadata={
                        "provider": "openrouter",
                        "use_case": use_case,
                        "tier": "free" if is_free else "paid",
                        "cost_optimized": True,
                        "top_model": model in top_models,
                        "popularity_rank": "top" if model in top_models else "popular" if is_free else "paid",
                        "models_tried": models_tried,
                        "fast_fail": True
                    },
                    success=True
                )
                
            except Exception as e:
                error_str = str(e)
                logger.warning(f"❌ Error with model {model}: {error_str}")
                last_error = error_str
                
                # For timeouts and overload, continue to next model (don't break)
                if "timeout" in error_str.lower() or "503" in error_str:
                    logger.info(f"⏭️ Model {model} overloaded/timeout, trying next model...")
                    continue
                
                # For other errors, also continue to next model
                continue
        
        # All models failed or we reached the limit
        if models_tried == 0:
            error_msg = f"No models attempted for {use_case} - all filtered out by budget constraints"
        else:
            error_msg = f"Failed after trying {models_tried} models for {use_case} (limited to {max_models_to_try} to prevent API blocking). Last error: {last_error}"
        
        logger.error(error_msg)
        
        return LLMResponse(
            content="",
            model_used="openrouter",
            success=False,
            error=error_msg,
            metadata={
                "provider": "openrouter",
                "use_case": use_case,
                "models_tried": models_tried,
                "max_attempts": max_models_to_try,
                "fast_fail_enabled": True
            }
        )
    
    def validate_request(self, request: LLMRequest) -> bool:
        """Validate the request object."""
        # For now, we assume the request from the factory is valid.
        # We can add more specific validation here if needed.
        return True
    
    def is_available(self) -> bool:
        """Check if OpenRouter service is available."""
        return OPENROUTER_AVAILABLE and self._available and self._client is not None
    
    def get_provider_name(self) -> str:
        """Get provider name."""
        return "openrouter"
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get cost optimization summary."""
        total_free_models = 0
        total_paid_models = 0
        
        for config in self.model_configs.values():
            for model, tier in config["models"]:
                if tier == ModelTier.FREE:
                    total_free_models += 1
                else:
                    total_paid_models += 1
        
        # TOP models list - updated with user preference
        top_models = [
            "meta-llama/llama-4-maverick:free",  # User specifically requested
            "deepseek/deepseek-r1:free",
            "deepseek/deepseek-chat-v3-0324:free", 
            "meta-llama/llama-3.3-70b-instruct:free"
        ]
        
        return {
            "provider": "openrouter",
            "cost_settings": {
                "max_monthly_spend": self.max_monthly_spend,
                "prefer_free_models": self.prefer_free_models,
                "free_only_mode": self.max_monthly_spend == 0.0
            },
            "available_models": {
                "free_models": total_free_models,
                "paid_models": total_paid_models,
                "top_free_models": len(top_models),
                "vision_free_models": 6  # llama-4-maverick, deepseek-chat, mistral-small, kimi-vl, qwen2.5-vl, llama-3.3
            },
            "optimization_strategy": "prioritize_top_free_models",
            "top_models": top_models,
            "capabilities": {
                "text_generation": "FREE - TOP MODELS",
                "code_generation": "FREE - DEEPSEEK SPECIALISTS",
                "translation": "FREE - GEMINI OPTIMIZED", 
                "validation": "FREE - REASONING MODELS",
                "vision_analysis": "FREE - 400B MOE",
                "script_generation": "FREE - CODING EXPERTS"
            }
        }
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get OpenRouter usage statistics with cost tracking."""
        if not self.is_available():
            return {"status": "unavailable", "error": "Service not available"}
        
        try:
            base_stats = {
                "provider": "openrouter",
                "status": "available",
                "cost_optimization": self.get_cost_summary()
            }
            
            if not USE_REQUESTS_FALLBACK and hasattr(self._client, 'credits'):
                # Get credits and usage
                credits = self._client.credits.get()
                rate_limits = self._client.calculate_rate_limits()
                
                base_stats.update({
                    "credits": {
                        "balance": credits.data.credits,
                        "usage": credits.data.usage
                    },
                    "rate_limits": rate_limits
                })
            
            return base_stats
            
        except Exception as e:
            logger.error(f"Error getting OpenRouter usage stats: {e}")
            return {
                "provider": "openrouter", 
                "status": "error",
                "error": str(e),
                "cost_optimization": self.get_cost_summary()
            }

    # Convenience methods for specific use cases
    def generate_gherkin(self, instructions: str, user_story: str = "", 
                        url: str = "", language: str = "en") -> Dict[str, Any]:
        """Generate Gherkin scenario using TOP FREE models."""
        prompt = f"""Generate a Gherkin scenario for the following:

Instructions: {instructions}
{f"User Story: {user_story}" if user_story else ""}
{f"URL: {url}" if url else ""}

Create a clear, well-structured Gherkin scenario with Given-When-Then format.
Respond in {"Spanish" if language == "es" else "English"}.
"""
        
        messages = [
            {"role": "system", "content": "You are an expert QA engineer specializing in Gherkin test scenarios."},
            {"role": "user", "content": prompt}
        ]
        
        logger.info("🎯 Generating Gherkin with TOP FREE models")
        return self.make_request({
            "use_case": "gherkin",
            "messages": messages,
            "max_tokens": 1024  # Conservative limit
        })

    def analyze_test_with_image(self, image_data: str, mime_type: str, 
                               test_objective: str, instructions: str = "") -> Dict[str, Any]:
        """Analyze test screenshots using TOP FREE vision models."""
        messages = [
            {
                "role": "system", 
                "content": "You are an expert test analyst. Analyze the provided screenshot and determine if the test objective was met."
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": f"""Test Objective: {test_objective}
{f"Additional Instructions: {instructions}" if instructions else ""}

Please analyze this screenshot and determine:
1. What is visible on the screen
2. Whether the test objective appears to be met
3. Any issues or concerns you notice
4. Confidence level in your assessment

Respond with detailed analysis."""},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime_type};base64,{image_data}"}
                    }
                ]
            }
        ]
        
        logger.info("📸 Analyzing screenshot with TOP FREE vision models")
        return self.make_request({
            "use_case": "test_analysis",
            "messages": messages,
            "has_images": True,
            "max_tokens": 1024  # Conservative for vision tasks
        })

    def validate_result(self, result_data: Dict[str, Any], 
                       test_objective: str, gherkin_scenario: str = "") -> Dict[str, Any]:
        """Validate test execution result using TOP FREE models only."""
        prompt = f"""Analyze this test execution result:

Test Objective: {test_objective}
{f"Gherkin Scenario: {gherkin_scenario}" if gherkin_scenario else ""}

Result Data: {result_data}

Determine if the test succeeded. Respond with JSON:
{{
    "validated_success": true/false,
    "validation_confidence": 0.0-1.0,
    "validation_reasoning": "explanation",
    "should_override_result": true/false
}}
"""
        
        messages = [
            {"role": "system", "content": "You are an expert test analyst. Respond only with valid JSON."},
            {"role": "user", "content": prompt}
        ]
        
        logger.info("🔍 Validating result with TOP FREE models")
        return self.make_request({
            "use_case": "validation",
            "messages": messages,
            "max_tokens": 512  # Very conservative for validation
        })


# Singleton instance for cost-optimized usage with TOP models
_openrouter_service = None

def get_openrouter_service(max_monthly_spend: float = None,
                          prefer_free_models: bool = True,
                          fast_fail_mode: bool = True) -> OpenRouterService:
    """Get singleton OpenRouter service instance with anti-blocking controls.

    Args:
        max_monthly_spend: Maximum monthly spend (None = use env var, 0 = TOP free models only)
        prefer_free_models: Always try TOP free models first
        fast_fail_mode: Enable fast-fail to prevent API blocking (recommended for web APIs)
    """
    global _openrouter_service
    if _openrouter_service is None:
        # Use environment variable if not specified
        if max_monthly_spend is None:
            max_monthly_spend = float(os.getenv("OPENROUTER_MAX_MONTHLY_SPEND", "0.0"))

        _openrouter_service = OpenRouterService(
            max_monthly_spend=max_monthly_spend,
            prefer_free_models=prefer_free_models,
            fast_fail_mode=fast_fail_mode
        )
        mode_info = "FAST-FAIL" if fast_fail_mode else "FULL-RETRY"
        budget_info = "FREE ONLY" if max_monthly_spend == 0.0 else "BUDGET CONTROLLED"
        logger.info(f"🎯 OpenRouter Service initialized - Mode: {mode_info} + {budget_info}")
    return _openrouter_service

def message_content_to_dict(content):
    # Si es texto
    if hasattr(content, 'text') and content.text is not None:
        return {"type": "text", "text": content.text}
    # Si es imagen
    elif hasattr(content, 'image') and content.image is not None:
        return {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{content.image.data}"
            }
        }
    # Si ya es dict (por si acaso)
    elif isinstance(content, dict):
        return content
    else:
        raise ValueError("Unknown content type for OpenRouter serialization")